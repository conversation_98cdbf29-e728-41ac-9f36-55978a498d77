# 使用 --platform 参数来指定目标平台
FROM --platform=linux/amd64 node:18-alpine

# Create app directory
WORKDIR /usr/src/app

# Copy the built application to the container
COPY dist .

# Install app dependencies
RUN mkdir logs && npm install --production && npm cache clean --force

ENV NODE_ENV=production
ENV KAFKAJS_NO_PARTITIONER_WARNING=1

CMD ["node", "--max-old-space-size=8192", "--max-semi-space-size=256", "index.js"]
