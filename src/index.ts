import Bull from 'bull';
import cluster from 'cluster';
import { Knex } from 'knex';
import { Config, createConfig, QueueConfig, WorkerName } from './config';
import { bootstrapDB, bootstrapInfluxDB, bootstrapClickhouse } from './db';
import { TimeTickRepository } from './repository/timeTickRepository';
import { TotalSumUpRepository } from './repository/totalSumUpRepository';
import { WalletDataRepository } from './repository/walletDataRepository';
import { TimeTickService } from './service/timeTickService';
import { InfluxDB } from '@influxdata/influxdb-client';
import { InfluxdbService } from './service/influxdbService';
import { WalletDataService } from './service/walletDataService';
import { WalletHistoryService } from './service/walletHistoryService';
import { InfluxDbRepository } from './repository/influxdbRepository';
import { <PERSON>ati<PERSON><PERSON><PERSON><PERSON>er, TimeTickPayloadHash } from './controller/statisticCtrl';
import { workerManager } from './worker';
import { JobHandler, STATISTIC_DIRECTION } from './handlers';
import logger from './logger';

import { Producer } from 'kafkajs';
import { createKafka } from './db/kafka';
import { createRedisClient } from './db';
import { TotalSumUpService } from './service/totalSumUpService';
import { Redis } from 'ioredis';
import { RedisRepository } from './repository/redisRepository';
import { PreStoreService } from './service/preStoreService';
import { ClickHouseClient } from '@clickhouse/client';
import { ClickhouseRepository } from './repository/clickhouseRepository';
import { WalletHistoryRepository } from './repository/walletHistoryRepository';
import { ClickhouseService } from './service/clickhouseService';
import { NotificationService } from './service/notificationService';
import { GameService } from './service/gameService';
import { GameRepository } from './repository/gameRepository';
import { regxTaskName } from './utils';

const MAX_RESTART_TIME = Number(process.env.MAX_PROCESS_RESTART_TIME) || 3;
logger.debug('🚀 ~ MAX_RESTART_TIME:', MAX_RESTART_TIME);

type CountWithTimeTick = {
  count: number;
  timeTick: Date;
};

const getRestartCount = (restartCount: Record<string, CountWithTimeTick>, queueName: string) => {
  if (!restartCount[queueName]) {
    restartCount[queueName] = {
      count: 0,
      timeTick: new Date(),
    };
  }

  return restartCount[queueName];
};

const setRestartCount = (restartCount: Record<string, CountWithTimeTick>, queueName: string, count: number) => {
  restartCount[queueName] = {
    count,
    timeTick: new Date(),
  };
};

interface WorkerInfo {
  QUEUE_NAME: string;
  JOB_NAME: string;
  SOURCE_MEASUREMENT: string;

  SOURCE_COUNT_MEASUREMENT: string;
}

interface WorkerProcessMessage {
  msg: string;
  processInfo: WorkerInfo;
}

function setupRepository({
  dbMaster,
  redis,
  sourceDataDB,
  clickhouseDB,
  config,
}: {
  dbMaster: Knex;
  redis: Redis;
  sourceDataDB: InfluxDB;
  clickhouseDB: ClickHouseClient;
  config: Config;
}) {
  const timeTickRepository = new TimeTickRepository({ dbMaster });
  const gameRepository = new GameRepository({ dbMaster });
  const totalSumUpRepository = new TotalSumUpRepository({ dbMaster });
  const walletDataRepository = new WalletDataRepository({ dbMaster });
  const redisRepository = new RedisRepository(redis);
  const influxdbRepository = new InfluxDbRepository(
    sourceDataDB,
    config.SOURCE_INFLUXDB_ORG,
    config.SOURCE_INFLUXDB_BUCKET,
    config.SOURCE_INFLUXDB_SPIN_MEASUREMENT,
  );
  const clickhouseRepository = new ClickhouseRepository({ config, client: clickhouseDB });
  const walletHistoryRepository = new WalletHistoryRepository(clickhouseDB);

  return {
    timeTickRepository,
    gameRepository,
    totalSumUpRepository,
    walletDataRepository,
    influxdbRepository,
    redisRepository,
    clickhouseRepository,
    walletHistoryRepository,
  };
}

function setupService({
  timeTickRepository,
  gameRepository,
  redisRepository,
  totalSumUpRepository,
  walletDataRepository,
  influxdbRepository,
  clickhouseRepository,
  walletHistoryRepository,
}: {
  timeTickRepository: TimeTickRepository;
  gameRepository: GameRepository;
  redisRepository: RedisRepository;
  totalSumUpRepository: TotalSumUpRepository;
  walletDataRepository: WalletDataRepository;
  influxdbRepository: InfluxDbRepository;
  clickhouseRepository: ClickhouseRepository;
  walletHistoryRepository: WalletHistoryRepository;
}) {
  const timeTickService = new TimeTickService(timeTickRepository);
  const gameService = new GameService(gameRepository);
  const totalSumUpService = new TotalSumUpService(totalSumUpRepository);
  const walletDataService = new WalletDataService(walletDataRepository);
  const influxdbService = new InfluxdbService(influxdbRepository);
  const preStoreService = new PreStoreService(redisRepository);
  const clickhouseService = new ClickhouseService(clickhouseRepository);
  const walletHistoryService = new WalletHistoryService(walletHistoryRepository);

  return {
    timeTickService,
    totalSumUpService,
    gameService,
    walletDataService,
    preStoreService,
    influxdbService,
    clickhouseService,
    walletHistoryService,
  };
}

function setupBusinessController({
  config,
  kafkaProducer,
  preStoreService,
  timeTickService,
  gameService,
  totalSumUpService,
  walletDataService,
  influxdbService,
  clickhouseService,
  walletHistoryService,
  notificationService,
}: {
  config: Config;
  kafkaProducer: Producer;
  preStoreService: PreStoreService;
  timeTickService: TimeTickService;
  gameService: GameService;
  totalSumUpService: TotalSumUpService;
  walletDataService: WalletDataService;
  influxdbService: InfluxdbService;
  clickhouseService: ClickhouseService;
  walletHistoryService: WalletHistoryService;
  notificationService: NotificationService;
}) {
  const statisticController = new StatisticController(
    config,
    kafkaProducer,
    timeTickService,
    gameService,
    influxdbService,
    clickhouseService,
    totalSumUpService,
    walletDataService,
    preStoreService,
    walletHistoryService,
    notificationService,
  );

  return {
    statisticController,
  };
}

function setupQueue({
  statisticController,
  confQueues,
  redisUrl,
  timeTickPayloadHash,
}: {
  statisticController: StatisticController;
  confQueues: QueueConfig[];
  redisUrl: string;
  timeTickPayloadHash: TimeTickPayloadHash;
}) {
  const jobHandler = new JobHandler({
    statisticController,
    confQueues,
    redisUrl,
    timeTickPayloadHash,
  });

  jobHandler.start();

  return jobHandler;
}

function spawnWorkerPerQueue({
  kafkaProducer,
  jobHandler,
  confQueues,
  kafkaTopic,
}: {
  kafkaProducer: Producer;
  jobHandler: JobHandler;
  confQueues: QueueConfig[];
  kafkaTopic: string;
}) {
  const restartCountMap: Record<string, CountWithTimeTick> = {};
  // 統一的 worker 資訊映射，包含創建和重啟所需的所有資訊
  const workerInfoMap: Record<
    string,
    {
      // 創建資訊
      queueName: string;
      direction: string;
      workerName: string;
      queueConfig: QueueConfig;
      // 運行時資訊
      workerId: number;
      pid?: number;
      // 重啟資訊
      QUEUE_NAME: string;
      JOB_NAME: string;
      WORKER_NAME: string;
      SOURCE_MEASUREMENT: string;
    }
  > = {};

  const spawnWorker = (qc: QueueConfig, postfix: STATISTIC_DIRECTION) => {
    let clusterEnv: Record<string, string> = {};

    clusterEnv = {
      ...process.env,
      QUEUE_NAME: `${qc.name}-${postfix}`,
      JOB_NAME: `${qc.name}-${postfix}`,
      WORKER_NAME: qc.workerName,
      SOURCE_MEASUREMENT: qc.sourceInfluxdbMeasurement,
    };

    const worker = cluster.fork(clusterEnv);

    // 統一記錄所有 worker 資訊
    workerInfoMap[worker.id] = {
      // 創建資訊
      queueName: `${qc.name}-${postfix}`,
      direction: postfix,
      workerName: qc.workerName,
      queueConfig: qc,
      // 運行時資訊
      workerId: worker.id,
      // 重啟資訊（環境變數）
      QUEUE_NAME: `${qc.name}-${postfix}`,
      JOB_NAME: `${qc.name}-${postfix}`,
      WORKER_NAME: qc.workerName,
      SOURCE_MEASUREMENT: qc.sourceInfluxdbMeasurement,
    };
  };

  for (let i = 0; i < confQueues.length; i++) {
    const qc = confQueues[i];
    console.debug('🚀 ~ qc:', qc);

    spawnWorker(qc, STATISTIC_DIRECTION.NEXT);

    if (jobHandler.shouldProcessBackward(qc.name)) {
      spawnWorker(qc, STATISTIC_DIRECTION.BACKWARD);
    }
  }

  cluster.on('online', (worker) => {
    const workerInfo = workerInfoMap[worker.id];
    if (workerInfo) {
      // 更新運行時 PID 資訊
      workerInfo.pid = worker.process.pid;
    }

    const workerDescription = workerInfo ? `[${workerInfo.queueName}] (${workerInfo.workerName})` : 'unknown';

    const workerDetails = {
      id: worker.id,
      pid: worker.process.pid,
      queueName: workerInfo?.queueName,
      workerName: workerInfo?.workerName,
      direction: workerInfo?.direction,
    };

    kafkaProducer.send({
      topic: kafkaTopic,
      messages: [{ value: `${process.pid} ⭕️ worker ${worker.process.pid} ${workerDescription} is online` }],
    });
    logger.info(`${process.pid} 👉 worker online: ${JSON.stringify(workerDetails)}`);
  });

  cluster.on('message', (worker, message) => {
    kafkaProducer.send({
      topic: kafkaTopic,
      messages: [{ value: `${process.pid} 👉 worker ${worker.process.pid} get message: ${JSON.stringify(message)}` }],
    });
    logger.info(`${process.pid} 👉 message from worker ${worker.process.pid}: ${JSON.stringify(message)}`);
  });

  cluster.on('exit', (worker, code, signal) => {
    // 通過 pid 反向查找 worker 資訊
    const workerInfo = Object.values(workerInfoMap).find((info) => info.pid === worker.process.pid);

    // 清理 worker 映射
    delete workerInfoMap[worker.id];

    kafkaProducer.send({
      topic: kafkaTopic,
      messages: [{ value: `${process.pid} ❌ worker ${worker.process.pid} is exit` }],
    });
    logger.info(`${process.pid} ❌ worker ${worker.process.pid} died with code: ${code}, and signal: ${signal}`);

    if (!workerInfo) {
      logger.error(`${process.pid} ❌ worker ${worker.process.pid} not found in workerInfoMap`);
      return;
    }

    const restartCount = getRestartCount(restartCountMap, workerInfo.QUEUE_NAME);

    const errMsg =
      restartCount.count > MAX_RESTART_TIME
        ? `${process.pid} ❌ ~ [${workerInfo.QUEUE_NAME}] ~ worker ${worker.process.pid} died with code: ${code}, and signal ${signal} (Spawn too many times, exit!)`
        : `${process.pid} ❌ ~ [${workerInfo.QUEUE_NAME}] ~ worker ${worker.process.pid} died with code: ${code}, and signal: ${signal} (Spawn in ${restartCount.count} times)`;
    kafkaProducer.send({ topic: kafkaTopic, messages: [{ value: errMsg }] });
    logger.error(errMsg);

    if (restartCount.count > MAX_RESTART_TIME) return;

    const newWorker = cluster.fork({
      ...process.env,
      QUEUE_NAME: workerInfo.QUEUE_NAME,
      JOB_NAME: workerInfo.JOB_NAME,
      WORKER_NAME: workerInfo.WORKER_NAME,
      SOURCE_MEASUREMENT: workerInfo.SOURCE_MEASUREMENT,
    });

    // 重啟時也要記錄到 workerInfoMap
    workerInfoMap[newWorker.id] = {
      // 使用原有的 worker 資訊
      queueName: workerInfo.QUEUE_NAME,
      direction: workerInfo.QUEUE_NAME.endsWith('-backward') ? 'backward' : 'next',
      workerName: workerInfo.WORKER_NAME,
      queueConfig: workerInfo.queueConfig,
      // 運行時資訊
      workerId: newWorker.id,
      // 重啟資訊
      QUEUE_NAME: workerInfo.QUEUE_NAME,
      JOB_NAME: workerInfo.JOB_NAME,
      WORKER_NAME: workerInfo.WORKER_NAME,
      SOURCE_MEASUREMENT: workerInfo.SOURCE_MEASUREMENT,
    };

    if (new Date().getTime() - restartCount.timeTick.getTime() < 1000 * 60 * 30) {
      setRestartCount(restartCountMap, workerInfo.QUEUE_NAME, restartCount.count + 1);
    }
  });

  return cluster;
}

// 根據環境變數獲取 Worker 的 QueueConfig
function getWorkerQueueConfig(): QueueConfig {
  const [, taskName] = regxTaskName.exec(process.env.QUEUE_NAME || '') || [];
  if (!taskName) {
    throw new Error('QUEUE_NAME environment variable is required for worker process');
  }

  const config = createConfig();
  const queueConfig = config.QUEUE_SETTINGS.find((q) => q.name === taskName);
  if (!queueConfig) {
    throw new Error(`No queue configuration found for ${taskName}`);
  }

  return queueConfig;
}

// Worker 進程設置 - 根據 QueueConfig 創建特定連接
async function setupWorkerProcess() {
  const queueConfig = getWorkerQueueConfig();
  const globalConfig = createConfig();

  // 使用 QueueConfig 特定的配置創建連接
  const dbMaster = bootstrapDB(queueConfig.dbUrlMaster, queueConfig.dbType);
  const redis = createRedisClient(queueConfig.redisUrl);
  const { producer: kafkaProducer } = await createKafka({
    clientId: globalConfig.KAFKA_CLIENT_ID,
    brokers: globalConfig.KAFKA_BROKERS,
    groupId: globalConfig.KAFKA_CONSUMER_GROUP_ID,
  });

  // 創建 Worker 特定的 InfluxDB 連接
  const sourceInfluxDB = bootstrapInfluxDB({
    dbUrl: queueConfig.sourceInfluxdbUrl,
    dbToken: queueConfig.sourceInfluxdbToken,
    dbTimeout: queueConfig.sourceInfluxdbConnectionTimeout,
  });

  const dbClickhouse = bootstrapClickhouse({
    url: queueConfig.clickhouseUrl,
    username: queueConfig.clickhouseUser,
    password: queueConfig.clickhousePassword,
    database: queueConfig.clickhouseDatabase,
  });

  // 創建 Worker 特定的 repository
  const {
    timeTickRepository,
    gameRepository,
    totalSumUpRepository,
    walletDataRepository,
    influxdbRepository,
    redisRepository,
    clickhouseRepository,
    walletHistoryRepository,
  } = setupRepository({
    dbMaster,
    redis,
    sourceDataDB: sourceInfluxDB,
    clickhouseDB: dbClickhouse,
    config: {
      ...globalConfig,
      // 使用 QueueConfig 特定的配置覆蓋所有 InfluxDB 相關配置
      SOURCE_INFLUXDB_URL: queueConfig.sourceInfluxdbUrl,
      SOURCE_INFLUXDB_ORG: queueConfig.sourceInfluxdbOrg,
      SOURCE_INFLUXDB_BUCKET: queueConfig.sourceInfluxdbBucket,
      SOURCE_INFLUXDB_TOKEN: queueConfig.sourceInfluxdbToken,
      SOURCE_INFLUXDB_CONNECTION_TIMEOUT: queueConfig.sourceInfluxdbConnectionTimeout,
      SOURCE_INFLUXDB_SPIN_MEASUREMENT: queueConfig.sourceInfluxdbMeasurement,
      SOURCE_INFLUXDB_WALLET_MEASUREMENT: queueConfig.sourceInfluxdbMeasurement,

      // 如果是 Table 相關的 Worker，也要覆蓋 TABLE 相關配置
      SOURCE_INFLUXDB_TABLE_URL: queueConfig.sourceInfluxdbUrl,
      SOURCE_INFLUXDB_TABLE_ORG: queueConfig.sourceInfluxdbOrg,
      SOURCE_INFLUXDB_TABLE_BUCKET: queueConfig.sourceInfluxdbBucket,
      SOURCE_INFLUXDB_TABLE_TOKEN: queueConfig.sourceInfluxdbToken,
      SOURCE_INFLUXDB_TABLE_MEASUREMENT: queueConfig.sourceInfluxdbMeasurement,
    },
  });

  // 創建 Worker 特定的 service
  const {
    timeTickService,
    gameService,
    influxdbService,
    totalSumUpService,
    walletDataService,
    preStoreService,
    clickhouseService,
    walletHistoryService,
  } = setupService({
    timeTickRepository,
    gameRepository,
    redisRepository,
    totalSumUpRepository,
    walletDataRepository,
    influxdbRepository,
    clickhouseRepository,
    walletHistoryRepository,
  });

  const notificationService = new NotificationService(dbClickhouse, dbMaster);

  // 創建 Worker 特定的 StatisticController
  const { statisticController } = setupBusinessController({
    kafkaProducer,
    config: globalConfig,
    timeTickService,
    gameService,
    totalSumUpService,
    walletDataService,
    influxdbService,
    preStoreService,
    clickhouseService,
    walletHistoryService,
    notificationService,
  });

  // 創建包含 QueueConfig 配置的 Worker 專用 config
  const workerConfig = {
    ...globalConfig,
    // 使用 QueueConfig 特定的配置覆蓋所有 InfluxDB 相關配置
    SOURCE_INFLUXDB_URL: queueConfig.sourceInfluxdbUrl,
    SOURCE_INFLUXDB_ORG: queueConfig.sourceInfluxdbOrg,
    SOURCE_INFLUXDB_BUCKET: queueConfig.sourceInfluxdbBucket,
    SOURCE_INFLUXDB_TOKEN: queueConfig.sourceInfluxdbToken,
    SOURCE_INFLUXDB_CONNECTION_TIMEOUT: queueConfig.sourceInfluxdbConnectionTimeout,
    SOURCE_INFLUXDB_SPIN_MEASUREMENT: queueConfig.sourceInfluxdbMeasurement,
    SOURCE_INFLUXDB_WALLET_MEASUREMENT: queueConfig.sourceInfluxdbMeasurement,

    // 如果是 Table 相關的 Worker，也要覆蓋 TABLE 相關配置
    SOURCE_INFLUXDB_TABLE_URL: queueConfig.sourceInfluxdbUrl,
    SOURCE_INFLUXDB_TABLE_ORG: queueConfig.sourceInfluxdbOrg,
    SOURCE_INFLUXDB_TABLE_BUCKET: queueConfig.sourceInfluxdbBucket,
    SOURCE_INFLUXDB_TABLE_TOKEN: queueConfig.sourceInfluxdbToken,
    SOURCE_INFLUXDB_TABLE_MEASUREMENT: queueConfig.sourceInfluxdbMeasurement,

    // Redis 配置
    REDIS_URL: queueConfig.redisUrl,
  };

  return { statisticController, globalConfig: workerConfig, redis };
}

async function main() {
  const config = createConfig();

  if (!cluster.isPrimary) {
    // === Worker 進程：使用 QueueConfig 特定配置 ===
    const { statisticController, globalConfig, redis } = await setupWorkerProcess();

    process.send?.({
      msg: `worker is ready: ${process.env.QUEUE_NAME}`,
      processInfo: {
        QUEUE_NAME: process.env.QUEUE_NAME,
        JOB_NAME: process.env.QUEUE_NAME,
        SOURCE_MEASUREMENT: process.env.SOURCE_MEASUREMENT,
        SOURCE_COUNT_MEASUREMENT: process.env.SOURCE_COUNT_MEASUREMENT,
      },
    } as WorkerProcessMessage);

    if (!process.env.WORKER_NAME || !process.env.QUEUE_NAME || !process.env.JOB_NAME) {
      const errorMsg = `❌ QUEUE_NAME, JOB_NAME, WORKER_NAME or is not given`;
      throw new Error(errorMsg);
    }

    const queue = new Bull(process.env.QUEUE_NAME, globalConfig.REDIS_URL, {
      settings: {
        maxStalledCount: 5,
      },
    });
    queue.process(
      process.env.JOB_NAME,
      1,
      workerManager({
        jobName: process.env.JOB_NAME,
        workerName: process.env.WORKER_NAME as WorkerName,
        statisticController,
        config: globalConfig,
        redis,
      }),
    );

    process.on('uncaughtException', (error: Error) => {
      const errorMsg = `❌ uncaughtException: ${error}`;
      logger.error(errorMsg);
      process.exit(1);
    });

    return;
  }

  // === Master 進程：使用全局配置（保持原邏輯）===
  const dbMaster = bootstrapDB(config.DB_URL_MASTER, config.DB_TYPE);
  const redis = createRedisClient(config.REDIS_URL);
  const sourceInfluxDB = bootstrapInfluxDB({
    dbUrl: config.SOURCE_INFLUXDB_URL,
    dbToken: config.SOURCE_INFLUXDB_TOKEN,
    dbTimeout: config.SOURCE_INFLUXDB_CONNECTION_TIMEOUT,
  });

  const { producer: kafkaProducer } = await createKafka({
    clientId: config.KAFKA_CLIENT_ID,
    brokers: config.KAFKA_BROKERS,
    groupId: config.KAFKA_CONSUMER_GROUP_ID,
  });

  const dbClickhouse = bootstrapClickhouse({
    url: config.CLICKHOUSE_URL,
    username: config.CLICKHOUSE_USER,
    password: config.CLICKHOUSE_PASSWORD,
    database: config.CLICKHOUSE_DATABASE,
  });

  const {
    timeTickRepository,
    gameRepository,
    totalSumUpRepository,
    walletDataRepository,
    influxdbRepository,
    redisRepository,
    clickhouseRepository,
    walletHistoryRepository,
  } = setupRepository({
    dbMaster,
    redis,
    sourceDataDB: sourceInfluxDB,
    clickhouseDB: dbClickhouse,
    config,
  });

  const {
    timeTickService,
    gameService,
    influxdbService,
    totalSumUpService,
    walletDataService,
    preStoreService,
    clickhouseService,
    walletHistoryService,
  } = setupService({
    timeTickRepository,
    gameRepository,
    redisRepository,
    totalSumUpRepository,
    walletDataRepository,
    influxdbRepository,
    clickhouseRepository,
    walletHistoryRepository,
  });

  const notificationService = new NotificationService(dbClickhouse, dbMaster);

  const { statisticController } = setupBusinessController({
    kafkaProducer,
    config,
    timeTickService,
    gameService,
    totalSumUpService,
    walletDataService,
    influxdbService,
    preStoreService,
    clickhouseService,
    walletHistoryService,
    notificationService,
  });

  const queueNames = config.QUEUE_SETTINGS.map((q) => q.name);
  const timeTickPayloadHash = await statisticController.setup({ queueNames });

  logger.info(`${process.pid} 👉 Env: ${JSON.stringify(config)}`);

  try {
    const jobHandler = setupQueue({
      statisticController,
      confQueues: config.QUEUE_SETTINGS,
      redisUrl: config.REDIS_URL,
      timeTickPayloadHash,
    });

    spawnWorkerPerQueue({
      kafkaProducer,
      jobHandler,
      confQueues: config.QUEUE_SETTINGS,
      kafkaTopic: config.KAFKA_TOPIC_STATISTIC_SYSTEM,
    });
  } catch (e: unknown) {
    kafkaProducer.send({
      topic: config.KAFKA_TOPIC_STATISTIC_SYSTEM,
      messages: [{ value: `❌ Error occurred: ${(e as Error).toString()}` }],
    });
  }

  process.on('uncaughtException', (error: Error) => {
    logger.error('Uncaught Exception:', {
      error: error,
      stack: error.stack,
    });

    setTimeout(() => {
      process.exit(1);
    }, 1000);
  });

  process.on('unhandledRejection', (error: Error) => {
    logger.error('Unhandled Rejection:', {
      error: error,
      stack: error.stack,
    });
  });
}

main().then(() => {});
