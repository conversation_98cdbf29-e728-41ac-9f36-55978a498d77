import Redis from 'ioredis';

export const createRedisClient = (redisUrl: string) => {
  // create a new Redis client instance
  const client = new Redis(redisUrl);

  client.on('error', (err) => console.error('Redis Client Error', err));

  // ioredis don't need to call connect explicitly
  return client;
};

export const REDIS_KEY = {
  sum_up_min: 'sum_up_min',
  sum_up_hourly: 'sum_up_hourly',
  sum_up_daily: 'sum_up_daily',
  sum_up_monthly: 'sum_up_monthly',
  sum_up_no_player_min: 'sum_up_no_player_min',
  sum_up_no_player_hourly: 'sum_up_no_player_hourly',
  sum_up_no_player_daily: 'sum_up_no_player_daily',
  sum_up_no_player_monthly: 'sum_up_no_player_monthly',

  sum_up_total_game_daily: 'sum_up_total_game_daily',
  sum_up_total_game_monthly: 'sum_up_total_game_monthly',
  sum_up_total_partner_daily: 'sum_up_total_partner_daily',
  sum_up_total_partner_monthly: 'sum_up_total_partner_monthly',
  sum_up_total_all_monthly: 'sum_up_total_all_monthly',
};
