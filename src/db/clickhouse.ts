import { createClient } from '@clickhouse/client';
import logger from '../logger';

export const bootstrapClickhouse = ({
  url,
  username,
  password,
  database,
}: {
  url: string;
  username: string;
  password: string;
  database: string;
}) => {
  logger.info(
    `${process.pid} 👉 ~ file: index.ts:5 ~ bootstrapDB createClickhouseClient: ~ dbUrl: ${url}; username: ${username}; database: ${database}`,
  );

  try {
    const client = createClient({
      url,
      username,
      password,
      database,
      max_open_connections: 20,
      clickhouse_settings: {
        date_time_input_format: 'best_effort',
      },
    });

    return client;
  } catch (error) {
    logger.error(`❌ [${process.pid}] createClickhouseClient ~ error: ${error}`);
    throw error;
  }
};
